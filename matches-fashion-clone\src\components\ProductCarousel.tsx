'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  designer: string;
  image: string;
  href: string;
}

interface ProductCarouselProps {
  title: string;
  subtitle: string;
  count: number;
  ctaText?: string;
  ctaHref: string;
  products: Product[];
  type?: 'just-in' | 'trending' | 'featured';
}

const ProductCarousel = ({
  title,
  subtitle,
  count,
  ctaText = 'Shop Now',
  ctaHref,
  products,
  type = 'just-in'
}: ProductCarouselProps) => {
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const scrollerRef = useRef<HTMLDivElement>(null);

  const checkScrollButtons = () => {
    if (scrollerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const scroller = scrollerRef.current;
    if (scroller) {
      scroller.addEventListener('scroll', checkScrollButtons);
      return () => scroller.removeEventListener('scroll', checkScrollButtons);
    }
  }, []);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollerRef.current) {
      const scrollAmount = 300;
      const newScrollLeft = scrollerRef.current.scrollLeft + 
        (direction === 'left' ? -scrollAmount : scrollAmount);
      
      scrollerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section className="homepage-component component-product-carousel relative bg-white py-4">
      <div className="product-carousel-wrapper relative">
        {/* Left Arrow */}
        <button
          className={`product-carousel-arrow arrow-prev absolute left-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white border border-gray-300 hover:border-black transition-colors ${
            !canScrollLeft ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          }`}
          onClick={() => scroll('left')}
          disabled={!canScrollLeft}
          aria-label="Previous products"
        >
          <ChevronLeft size={16} className="mx-auto" />
        </button>

        {/* Scrollable Container */}
        <div
          ref={scrollerRef}
          className="product-carousel-scroller overflow-x-auto hide-scrollbar px-12"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          <div className="product-carousel-items flex space-x-3 min-w-max">
            {/* First Item - Info Card with exact dimensions */}
            <div className="item-card item-wide flex-shrink-0 w-72 h-80 bg-gray-50 flex flex-col justify-center items-center text-center p-6">
              <Link href={ctaHref} className="item-info block">
                <h3 className="mb-4">
                  <span className="homepage-skeleton number block text-3xl font-bold mb-1">
                    {count}
                  </span>
                  <span className="homepage-skeleton title block text-sm font-medium uppercase tracking-wide mb-1">
                    {title}
                  </span>
                  <span className="homepage-skeleton subtitle block text-xs text-gray-600 uppercase tracking-wide">
                    {subtitle}
                  </span>
                </h3>
                <p className="cta homepage-skeleton flex items-center justify-center space-x-1 text-xs font-medium uppercase tracking-wide hover:text-gray-600 transition-colors">
                  <span>{ctaText}</span>
                  <svg className="cta-arrow" height="6" viewBox="0 0 4 8" width="3" xmlns="http://www.w3.org/2000/svg">
                    <path d="m0 0 4 4-4 4z" fill="currentColor"></path>
                  </svg>
                </p>
              </Link>
            </div>

            {/* Product Items - Exact dimensions matching original */}
            {products.map((product, index) => (
              <Link
                key={product.id}
                href={product.href}
                className="item-card product-carousel-item flex-shrink-0 w-48 h-80 group cursor-pointer"
                aria-label={`${product.designer}. ${product.name}`}
              >
                <figure className="h-full">
                  <div className="image-wrapper h-5/6 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden group-hover:opacity-90 transition-opacity">
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-gray-500 text-xs">Product Image</span>
                    </div>
                  </div>
                  <figcaption className="designer h-1/6 flex items-center justify-center text-xs font-medium text-gray-700 group-hover:text-black transition-colors">
                    {product.designer}
                  </figcaption>
                </figure>
              </Link>
            ))}

            {/* Discover More Item - Matching dimensions */}
            <Link
              href={ctaHref}
              className="item-card product-carousel-item item-discover flex-shrink-0 w-48 h-80 bg-black text-white flex flex-col justify-center items-center text-center p-6 hover:bg-gray-800 transition-colors"
            >
              <h3 className="homepage-skeleton discover-text text-sm font-medium mb-3">
                Discover the latest arrivals
              </h3>
              <p className="cta homepage-skeleton flex items-center space-x-1 text-xs font-medium uppercase tracking-wide">
                <span>{ctaText}</span>
                <svg className="cta-arrow" height="6" viewBox="0 0 4 8" width="3" xmlns="http://www.w3.org/2000/svg">
                  <path d="m0 0 4 4-4 4z" fill="currentColor"></path>
                </svg>
              </p>
            </Link>
          </div>
        </div>

        {/* Right Arrow */}
        <button
          className={`product-carousel-arrow arrow-next absolute right-2 top-1/2 transform -translate-y-1/2 z-10 w-10 h-10 bg-white border border-gray-300 hover:border-black transition-colors ${
            !canScrollRight ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          }`}
          onClick={() => scroll('right')}
          disabled={!canScrollRight}
          aria-label="Next products"
        >
          <ChevronRight size={16} className="mx-auto" />
        </button>
      </div>

      <style jsx>{`
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </section>
  );
};

export default ProductCarousel;
