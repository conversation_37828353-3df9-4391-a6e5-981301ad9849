﻿import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen">
      <section className="min-h-screen flex items-center justify-center bg-white">
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <Link href="/mens" className="group relative overflow-hidden">
              <div className="aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-400 flex items-center justify-center">
                <div className="text-center text-white">
                  <h2 className="text-4xl font-bold mb-4">SHOP MENS</h2>
                  <p className="text-lg">Shop all mens items on MATCHES</p>
                </div>
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300" />
            </Link>
            
            <Link href="/womens" className="group relative overflow-hidden">
              <div className="aspect-[4/5] bg-gradient-to-br from-gray-300 to-gray-500 flex items-center justify-center">
                <div className="text-center text-white">
                  <h2 className="text-4xl font-bold mb-4">SHOP WOMENS</h2>
                  <p className="text-lg">Shop all womens items on MATCHES</p>
                </div>
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300" />
            </Link>
          </div>
          
          {/* Shop by Category */}
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold mb-8 uppercase tracking-wide">Shop by Category</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
              {[
                { name: "MENS JUST IN", href: "/mens/just-in/just-in-this-month" },
                { name: "MENS DESIGNERS", href: "/mens/designers" },
                { name: "MENS CLOTHING", href: "/mens/shop/clothing" },
                { name: "MENS SHOES", href: "/mens/shop/shoes" },
                { name: "MENS BAGS", href: "/mens/shop/bags" },
                { name: "MENS ACCESSORIES", href: "/mens/shop/accessories" },
                { name: "MENS OUTLET", href: "/mens/sale" },
              ].map((category, index) => (
                <Link
                  key={index}
                  href={category.href}
                  className="group block"
                >
                  <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center group-hover:from-gray-200 group-hover:to-gray-300 transition-all duration-300">
                    <span className="text-xs text-gray-600">Image</span>
                  </div>
                  <h3 className="text-xs font-medium uppercase tracking-wide text-center">
                    {category.name}
                  </h3>
                </Link>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-12">
            {[
              { name: "WOMENS JUST IN", href: "/womens/just-in/just-in-this-month" },
              { name: "WOMENS DESIGNERS", href: "/womens/designers" },
              { name: "WOMENS CLOTHING", href: "/womens/shop/clothing" },
              { name: "WOMENS SHOES", href: "/womens/shop/shoes" },
              { name: "WOMENS BAGS", href: "/womens/shop/bags" },
              { name: "WOMENS ACCESSORIES", href: "/womens/shop/accessories" },
              { name: "WOMENS OUTLET", href: "/womens/sale" },
            ].map((category, index) => (
              <Link
                key={index}
                href={category.href}
                className="group block"
              >
                <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center group-hover:from-gray-200 group-hover:to-gray-300 transition-all duration-300">
                  <span className="text-xs text-gray-600">Image</span>
                </div>
                <h3 className="text-xs font-medium uppercase tracking-wide text-center">
                  {category.name}
                </h3>
              </Link>
            ))}
          </div>

          {/* Top Designers */}
          <div className="text-center">
            <h2 className="text-xl font-bold mb-8 uppercase tracking-wide">Top Designers</h2>
            <div className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-12 gap-4 text-sm">
              {[
                'Gucci', 'Raey', 'Saint Laurent', 'Bottega Veneta', 'The Row', 'Loewe',
                'Toteme', 'Zimmermann', 'Valentino Garavani', 'Balenciaga', 'Alexander McQueen', 'Khaite'
              ].map((designer, index) => (
                <Link
                  key={index}
                  href={`/womens/designers/${designer.toLowerCase().replace(/\s+/g, '-')}`}
                  className="text-gray-600 hover:text-black transition-colors"
                >
                  {designer}
                </Link>
              ))}
            </div>
          </div>

          {/* About Section */}
          <div className="mt-16 text-center max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-6">MATCHES: Luxury For Every Moment</h2>
            <p className="text-gray-600 leading-relaxed mb-6">
              Welcome to the ultimate destination for a luxury shopping experience. With over 30 years of fashion expertise,
              MATCHES offers a curated edit of 450+ established and innovative designer brands, from Gucci and The Row to
              Saint Laurent, Bottega Veneta and more luxury fashion icons.
            </p>
            <p className="text-gray-600 leading-relaxed">
              Our buyers and Private Shopping team bring you the best designer clothing, bags, shoes, fine jewellery,
              accessories and homeware, and you'll be the first to shop over 1,000 new products every week, including
              hand-picked limited-edition pieces and exclusive collaborations.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
