{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <section className=\"min-h-screen flex items-center justify-center bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12\">\n            <Link href=\"/mens\" className=\"group relative overflow-hidden\">\n              <div className=\"aspect-[4/5] bg-gradient-to-br from-gray-200 to-gray-400 flex items-center justify-center\">\n                <div className=\"text-center text-white\">\n                  <h2 className=\"text-4xl font-bold mb-4\">SHOP MENS</h2>\n                  <p className=\"text-lg\">Shop all mens items on MATCHES</p>\n                </div>\n              </div>\n              <div className=\"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300\" />\n            </Link>\n            \n            <Link href=\"/womens\" className=\"group relative overflow-hidden\">\n              <div className=\"aspect-[4/5] bg-gradient-to-br from-gray-300 to-gray-500 flex items-center justify-center\">\n                <div className=\"text-center text-white\">\n                  <h2 className=\"text-4xl font-bold mb-4\">SHOP WOMENS</h2>\n                  <p className=\"text-lg\">Shop all womens items on MATCHES</p>\n                </div>\n              </div>\n              <div className=\"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300\" />\n            </Link>\n          </div>\n          \n          {/* Shop by Category */}\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-2xl font-bold mb-8 uppercase tracking-wide\">Shop by Category</h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4\">\n              {[\n                { name: \"MENS JUST IN\", href: \"/mens/just-in/just-in-this-month\" },\n                { name: \"MENS DESIGNERS\", href: \"/mens/designers\" },\n                { name: \"MENS CLOTHING\", href: \"/mens/shop/clothing\" },\n                { name: \"MENS SHOES\", href: \"/mens/shop/shoes\" },\n                { name: \"MENS BAGS\", href: \"/mens/shop/bags\" },\n                { name: \"MENS ACCESSORIES\", href: \"/mens/shop/accessories\" },\n                { name: \"MENS OUTLET\", href: \"/mens/sale\" },\n              ].map((category, index) => (\n                <Link\n                  key={index}\n                  href={category.href}\n                  className=\"group block\"\n                >\n                  <div className=\"aspect-square bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center group-hover:from-gray-200 group-hover:to-gray-300 transition-all duration-300\">\n                    <span className=\"text-xs text-gray-600\">Image</span>\n                  </div>\n                  <h3 className=\"text-xs font-medium uppercase tracking-wide text-center\">\n                    {category.name}\n                  </h3>\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-12\">\n            {[\n              { name: \"WOMENS JUST IN\", href: \"/womens/just-in/just-in-this-month\" },\n              { name: \"WOMENS DESIGNERS\", href: \"/womens/designers\" },\n              { name: \"WOMENS CLOTHING\", href: \"/womens/shop/clothing\" },\n              { name: \"WOMENS SHOES\", href: \"/womens/shop/shoes\" },\n              { name: \"WOMENS BAGS\", href: \"/womens/shop/bags\" },\n              { name: \"WOMENS ACCESSORIES\", href: \"/womens/shop/accessories\" },\n              { name: \"WOMENS OUTLET\", href: \"/womens/sale\" },\n            ].map((category, index) => (\n              <Link\n                key={index}\n                href={category.href}\n                className=\"group block\"\n              >\n                <div className=\"aspect-square bg-gradient-to-br from-gray-100 to-gray-200 mb-3 flex items-center justify-center group-hover:from-gray-200 group-hover:to-gray-300 transition-all duration-300\">\n                  <span className=\"text-xs text-gray-600\">Image</span>\n                </div>\n                <h3 className=\"text-xs font-medium uppercase tracking-wide text-center\">\n                  {category.name}\n                </h3>\n              </Link>\n            ))}\n          </div>\n\n          {/* Top Designers */}\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-bold mb-8 uppercase tracking-wide\">Top Designers</h2>\n            <div className=\"grid grid-cols-3 md:grid-cols-6 lg:grid-cols-12 gap-4 text-sm\">\n              {[\n                'Gucci', 'Raey', 'Saint Laurent', 'Bottega Veneta', 'The Row', 'Loewe',\n                'Toteme', 'Zimmermann', 'Valentino Garavani', 'Balenciaga', 'Alexander McQueen', 'Khaite'\n              ].map((designer, index) => (\n                <Link\n                  key={index}\n                  href={`/womens/designers/${designer.toLowerCase().replace(/\\s+/g, '-')}`}\n                  className=\"text-gray-600 hover:text-black transition-colors\"\n                >\n                  {designer}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* About Section */}\n          <div className=\"mt-16 text-center max-w-4xl mx-auto\">\n            <h2 className=\"text-2xl font-bold mb-6\">MATCHES: Luxury For Every Moment</h2>\n            <p className=\"text-gray-600 leading-relaxed mb-6\">\n              Welcome to the ultimate destination for a luxury shopping experience. With over 30 years of fashion expertise,\n              MATCHES offers a curated edit of 450+ established and innovative designer brands, from Gucci and The Row to\n              Saint Laurent, Bottega Veneta and more luxury fashion icons.\n            </p>\n            <p className=\"text-gray-600 leading-relaxed\">\n              Our buyers and Private Shopping team bring you the best designer clothing, bags, shoes, fine jewellery,\n              accessories and homeware, and you'll be the first to shop over 1,000 new products every week, including\n              hand-picked limited-edition pieces and exclusive collaborations.\n            </p>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;;kDAC3B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;;kDAC7B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAChE,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAgB,MAAM;oCAAmC;oCACjE;wCAAE,MAAM;wCAAkB,MAAM;oCAAkB;oCAClD;wCAAE,MAAM;wCAAiB,MAAM;oCAAsB;oCACrD;wCAAE,MAAM;wCAAc,MAAM;oCAAmB;oCAC/C;wCAAE,MAAM;wCAAa,MAAM;oCAAkB;oCAC7C;wCAAE,MAAM;wCAAoB,MAAM;oCAAyB;oCAC3D;wCAAE,MAAM;wCAAe,MAAM;oCAAa;iCAC3C,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,SAAS,IAAI;wCACnB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;0DAE1C,8OAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;;uCARX;;;;;;;;;;;;;;;;kCAeb,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,MAAM;gCAAkB,MAAM;4BAAqC;4BACrE;gCAAE,MAAM;gCAAoB,MAAM;4BAAoB;4BACtD;gCAAE,MAAM;gCAAmB,MAAM;4BAAwB;4BACzD;gCAAE,MAAM;gCAAgB,MAAM;4BAAqB;4BACnD;gCAAE,MAAM;gCAAe,MAAM;4BAAoB;4BACjD;gCAAE,MAAM;gCAAsB,MAAM;4BAA2B;4BAC/D;gCAAE,MAAM;gCAAiB,MAAM;4BAAe;yBAC/C,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,SAAS,IAAI;gCACnB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;kDAE1C,8OAAC;wCAAG,WAAU;kDACX,SAAS,IAAI;;;;;;;+BARX;;;;;;;;;;kCAeX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAC/D,8OAAC;gCAAI,WAAU;0CACZ;oCACC;oCAAS;oCAAQ;oCAAiB;oCAAkB;oCAAW;oCAC/D;oCAAU;oCAAc;oCAAsB;oCAAc;oCAAqB;iCAClF,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,CAAC,kBAAkB,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;wCACxE,WAAU;kDAET;uCAJI;;;;;;;;;;;;;;;;kCAWb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAKlD,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}