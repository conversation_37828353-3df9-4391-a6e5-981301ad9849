import Link from "next/link";

export default function WomensPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-300"></div>
        <div className="relative z-10 text-center text-black">
          <p className="text-sm uppercase tracking-wide mb-4">introducing the new collections</p>
          <h1 className="text-6xl md:text-8xl font-bold mb-6">24/7 Style</h1>
          <button className="luxury-button">SHOP</button>
        </div>
      </section>

      {/* Navigation Bar */}
      <nav className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex justify-center space-x-8 py-4">
            <Link href="/womens/designers" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Designers
            </Link>
            <Link href="/womens/shop/clothing" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Clothing
            </Link>
            <Link href="/womens/shop/shoes" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Shoes
            </Link>
            <Link href="/womens/shop/bags" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Bags
            </Link>
            <Link href="/womens/shop/accessories" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Accessories
            </Link>
            <Link href="/womens/shop/jewellery-and-watches" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Fine Jewellery
            </Link>
            <Link href="/womens/shop/homeware" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Home
            </Link>
            <Link href="/womens/stories" className="text-sm font-medium uppercase tracking-wide hover:text-gray-600">
              Stories
            </Link>
          </div>
        </div>
      </nav>

      {/* Featured Content Grid */}
      <section className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Featured Item 1 */}
          <Link href="/womens/shop/clothing/coats" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Coat Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">in focus: gucci</p>
              <h3 className="text-lg font-medium">Most-Coveted Coats</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 2 */}
          <Link href="/womens/shop/shoes/heels" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Heels Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">featuring khaite</p>
              <h3 className="text-lg font-medium">Midas Touch: Kitten Heels</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 3 */}
          <Link href="/womens/designers/gabriela-hearst" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Tailoring Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">tonal grey tailoring</p>
              <h3 className="text-lg font-medium">Gabriela Hearst</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 4 */}
          <Link href="/womens/lists/new-season-heros/bags" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Bags Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">Simone rocha & more</p>
              <h3 className="text-lg font-medium">Wishlist-Worthy Bags</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 5 */}
          <Link href="/womens/lists/trending" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Trending Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">including 16arlington</p>
              <h3 className="text-lg font-medium">Trending Designers</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          {/* Featured Item 6 */}
          <Link href="/womens/lists/for-skiing" className="group">
            <div className="aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Ski Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">slope style</p>
              <h3 className="text-lg font-medium">Ski: Fashion Meets Function</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>
        </div>

        {/* Large Featured Items */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
          <Link href="/womens/lists/for-skiing" className="group">
            <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Large Ski Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">slope style</p>
              <h3 className="text-xl font-medium">Ski: Fashion Meets Function</h3>
              <button className="text-sm font-medium uppercase tracking-wide">SHOP</button>
            </div>
          </Link>

          <Link href="/stories/2024/02/fashion-valentines-day-gifting-dressing" className="group">
            <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Valentine's Image</span>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xs uppercase tracking-wide text-gray-500">curated by matches</p>
              <h3 className="text-xl font-medium">The Valentine's Day Edit</h3>
              <button className="text-sm font-medium uppercase tracking-wide">read and SHOP</button>
            </div>
          </Link>
        </div>
      </section>
    </div>
  );
}
