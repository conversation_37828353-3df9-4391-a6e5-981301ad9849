{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/app/delivery/page.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function DeliveryPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Breadcrumb */}\n      <div className=\"max-w-7xl mx-auto px-4 py-4\">\n        <nav className=\"text-sm text-gray-500\">\n          <Link href=\"/\" className=\"hover:text-black\">Home</Link>\n          <span className=\"mx-2\">/</span>\n          <span className=\"text-black\">Delivery</span>\n        </nav>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto px-4 py-12\">\n        <h1 className=\"text-4xl font-bold mb-8\">Delivery Information</h1>\n\n        <div className=\"space-y-8\">\n          {/* UK Delivery */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">UK Delivery</h2>\n            <div className=\"bg-gray-50 p-6 space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h3 className=\"font-medium mb-2\">Standard Delivery</h3>\n                  <p className=\"text-sm text-gray-600 mb-2\">3-5 working days</p>\n                  <p className=\"text-sm\">FREE on orders over £200</p>\n                  <p className=\"text-sm\">£5 on orders under £200</p>\n                </div>\n                <div>\n                  <h3 className=\"font-medium mb-2\">Express Delivery</h3>\n                  <p className=\"text-sm text-gray-600 mb-2\">Next working day</p>\n                  <p className=\"text-sm\">£15 (order by 2pm)</p>\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* International Delivery */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">International Delivery</h2>\n            <div className=\"bg-gray-50 p-6\">\n              <p className=\"text-gray-600 mb-4\">\n                We deliver to over 190 countries worldwide. Delivery times and costs vary by destination.\n              </p>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                <div>\n                  <h4 className=\"font-medium\">Europe</h4>\n                  <p className=\"text-gray-600\">3-7 working days</p>\n                  <p>From £15</p>\n                </div>\n                <div>\n                  <h4 className=\"font-medium\">USA & Canada</h4>\n                  <p className=\"text-gray-600\">5-10 working days</p>\n                  <p>From £25</p>\n                </div>\n                <div>\n                  <h4 className=\"font-medium\">Rest of World</h4>\n                  <p className=\"text-gray-600\">7-14 working days</p>\n                  <p>From £35</p>\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* Order Processing */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Order Processing</h2>\n            <div className=\"space-y-4\">\n              <p className=\"text-gray-600\">\n                Orders are processed Monday to Friday, excluding bank holidays. Orders placed after 2pm on Friday \n                will be processed the following Monday.\n              </p>\n              <ul className=\"list-disc list-inside space-y-2 text-gray-600\">\n                <li>You will receive an order confirmation email immediately after placing your order</li>\n                <li>A dispatch confirmation with tracking information will be sent when your order ships</li>\n                <li>Track your order using the link provided in your dispatch email</li>\n              </ul>\n            </div>\n          </section>\n\n          {/* Special Services */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Special Delivery Services</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"border border-gray-200 p-6\">\n                <h3 className=\"font-medium mb-3\">Saturday Delivery</h3>\n                <p className=\"text-sm text-gray-600 mb-2\">Available for UK orders</p>\n                <p className=\"text-sm\">£25 additional charge</p>\n              </div>\n              <div className=\"border border-gray-200 p-6\">\n                <h3 className=\"font-medium mb-3\">Nominated Day Delivery</h3>\n                <p className=\"text-sm text-gray-600 mb-2\">Choose your delivery date</p>\n                <p className=\"text-sm\">£20 additional charge</p>\n              </div>\n            </div>\n          </section>\n\n          {/* Contact */}\n          <section className=\"bg-gray-50 p-6 text-center\">\n            <h3 className=\"font-semibold mb-2\">Need help with your delivery?</h3>\n            <p className=\"text-gray-600 mb-4\">Contact our customer service team for assistance</p>\n            <Link href=\"/help\" className=\"luxury-button-outline\">\n              Contact Us\n            </Link>\n          </section>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAmB;;;;;;sCAC5C,8OAAC;4BAAK,WAAU;sCAAO;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAa;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCAExC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAU;;;;;;sEACvB,8OAAC;4DAAE,WAAU;sEAAU;;;;;;;;;;;;8DAEzB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmB;;;;;;sEACjC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;0EAAE;;;;;;;;;;;;kEAEL,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;0EAAE;;;;;;;;;;;;kEAEL,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;0EAC7B,8OAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOX,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAI7B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAM7B,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjE", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}