{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/app/help/page.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function HelpPage() {\n  const helpCategories = [\n    {\n      title: 'Orders & Delivery',\n      links: [\n        { name: 'Track your order', href: '/help/track-order' },\n        { name: 'Delivery information', href: '/delivery' },\n        { name: 'Delivery charges', href: '/help/delivery-charges' },\n        { name: 'International delivery', href: '/help/international-delivery' },\n      ]\n    },\n    {\n      title: 'Returns & Exchanges',\n      links: [\n        { name: 'Return policy', href: '/returns' },\n        { name: 'How to return', href: '/help/how-to-return' },\n        { name: 'Exchanges', href: '/help/exchanges' },\n        { name: 'Refunds', href: '/help/refunds' },\n      ]\n    },\n    {\n      title: 'Account & Shopping',\n      links: [\n        { name: 'Create an account', href: '/help/create-account' },\n        { name: 'Manage your account', href: '/help/manage-account' },\n        { name: 'Size guide', href: '/size-guide' },\n        { name: 'Product care', href: '/help/product-care' },\n      ]\n    },\n    {\n      title: 'Payment & Pricing',\n      links: [\n        { name: 'Payment methods', href: '/help/payment-methods' },\n        { name: 'Currency & pricing', href: '/help/currency-pricing' },\n        { name: 'Gift cards', href: '/gift-cards' },\n        { name: 'Promotional codes', href: '/help/promotional-codes' },\n      ]\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Breadcrumb */}\n      <div className=\"max-w-7xl mx-auto px-4 py-4\">\n        <nav className=\"text-sm text-gray-500\">\n          <Link href=\"/\" className=\"hover:text-black\">Home</Link>\n          <span className=\"mx-2\">/</span>\n          <span className=\"text-black\">Help Centre</span>\n        </nav>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 py-12\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold mb-4\">Help Centre</h1>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">\n            Find answers to your questions about shopping, delivery, returns, and more. \n            Our customer service team is here to help you with your luxury shopping experience.\n          </p>\n        </div>\n\n        {/* Search */}\n        <div className=\"max-w-2xl mx-auto mb-12\">\n          <div className=\"relative\">\n            <input\n              type=\"text\"\n              placeholder=\"Search for help topics...\"\n              className=\"w-full px-6 py-4 border border-gray-300 focus:outline-none focus:border-black text-lg\"\n            />\n            <button className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400\">\n              Search\n            </button>\n          </div>\n        </div>\n\n        {/* Help Categories */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\">\n          {helpCategories.map((category, index) => (\n            <div key={index} className=\"space-y-4\">\n              <h3 className=\"font-semibold text-lg\">{category.title}</h3>\n              <ul className=\"space-y-2\">\n                {category.links.map((link, linkIndex) => (\n                  <li key={linkIndex}>\n                    <Link\n                      href={link.href}\n                      className=\"text-gray-600 hover:text-black transition-colors text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Contact Section */}\n        <div className=\"bg-gray-50 p-8 text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Still need help?</h2>\n          <p className=\"text-gray-600 mb-6\">\n            Our customer service team is available to assist you with any questions.\n          </p>\n          <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4\">\n            <button className=\"luxury-button\">\n              Live Chat\n            </button>\n            <button className=\"luxury-button-outline\">\n              Email Us\n            </button>\n          </div>\n          <div className=\"mt-6 text-sm text-gray-500\">\n            <p>Customer Service Hours: Monday - Friday, 9AM - 6PM GMT</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAoB,MAAM;gBAAoB;gBACtD;oBAAE,MAAM;oBAAwB,MAAM;gBAAY;gBAClD;oBAAE,MAAM;oBAAoB,MAAM;gBAAyB;gBAC3D;oBAAE,MAAM;oBAA0B,MAAM;gBAA+B;aACxE;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAiB,MAAM;gBAAW;gBAC1C;oBAAE,MAAM;oBAAiB,MAAM;gBAAsB;gBACrD;oBAAE,MAAM;oBAAa,MAAM;gBAAkB;gBAC7C;oBAAE,MAAM;oBAAW,MAAM;gBAAgB;aAC1C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAqB,MAAM;gBAAuB;gBAC1D;oBAAE,MAAM;oBAAuB,MAAM;gBAAuB;gBAC5D;oBAAE,MAAM;oBAAc,MAAM;gBAAc;gBAC1C;oBAAE,MAAM;oBAAgB,MAAM;gBAAqB;aACpD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAmB,MAAM;gBAAwB;gBACzD;oBAAE,MAAM;oBAAsB,MAAM;gBAAyB;gBAC7D;oBAAE,MAAM;oBAAc,MAAM;gBAAc;gBAC1C;oBAAE,MAAM;oBAAqB,MAAM;gBAA0B;aAC9D;QACH;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAmB;;;;;;sCAC5C,8OAAC;4BAAK,WAAU;sCAAO;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAa;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAkC;;;;;;;;;;;;kCAOjD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAoE;;;;;;;;;;;;;;;;;kCAO1F,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAG,WAAU;kDAAyB,SAAS,KAAK;;;;;;kDACrD,8OAAC;wCAAG,WAAU;kDACX,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACzB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL;;;;;;;;;;;+BAJL;;;;;;;;;;kCAmBd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAgB;;;;;;kDAGlC,8OAAC;wCAAO,WAAU;kDAAwB;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,OAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}