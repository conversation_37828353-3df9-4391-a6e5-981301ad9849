{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/app/womens/shop/clothing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Filter, Grid, List, ChevronDown } from 'lucide-react';\n\nexport default function WomensClothingPage() {\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [sortBy, setSortBy] = useState('newest');\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Mock product data\n  const products = Array.from({ length: 24 }, (_, i) => ({\n    id: i + 1,\n    name: `Designer Item ${i + 1}`,\n    brand: ['Gucci', 'Saint Laurent', 'Bottega Veneta', 'The Row', '<PERSON>haite'][i % 5],\n    price: Math.floor(Math.random() * 2000) + 200,\n    originalPrice: Math.floor(Math.random() * 2000) + 200,\n    isOnSale: Math.random() > 0.7,\n    image: `product-${i + 1}`,\n    category: ['Dresses', 'Coats', 'Tops', 'Trousers', 'Knitwear'][i % 5],\n  }));\n\n  const categories = [\n    'Shop all',\n    'Activewear',\n    'Beachwear',\n    'Bridal',\n    'Cardigans',\n    'Coats',\n    'Denim',\n    'Dresses',\n    'Jackets',\n    'Jeans',\n    'Jumpsuits',\n    'Knitwear',\n    'Lingerie and nightwear',\n    'Loungewear',\n    'Matching sets',\n    'Skirts',\n    'Suits',\n    'Swimwear',\n    'Tops',\n    'Trousers',\n  ];\n\n  const designers = [\n    'ALAÏA',\n    'Alexander McQueen',\n    'Balenciaga',\n    'Bottega Veneta',\n    'Dolce & Gabbana',\n    'Erdem',\n    'Gabriela Hearst',\n    'Gucci',\n    'Isabel Marant',\n    'Jacquemus',\n    'Khaite',\n    'LOEWE',\n    'Max Mara',\n    'Moncler',\n    'Saint Laurent',\n    'The Row',\n    'Toteme',\n    'Valentino Garavani',\n    'Zimmermann',\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Breadcrumb */}\n      <div className=\"max-w-7xl mx-auto px-4 py-4\">\n        <nav className=\"text-sm text-gray-500\">\n          <Link href=\"/\" className=\"hover:text-black\">Home</Link>\n          <span className=\"mx-2\">/</span>\n          <Link href=\"/womens\" className=\"hover:text-black\">Women</Link>\n          <span className=\"mx-2\">/</span>\n          <span className=\"text-black\">Clothing</span>\n        </nav>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex\">\n          {/* Sidebar Filters */}\n          <aside className={`w-64 pr-8 ${showFilters ? 'block' : 'hidden'} lg:block`}>\n            <div className=\"space-y-8\">\n              {/* Categories */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Clothing</h3>\n                <ul className=\"space-y-2\">\n                  {categories.map((category, index) => (\n                    <li key={index}>\n                      <Link\n                        href={`/womens/shop/clothing/${category.toLowerCase().replace(/\\s+/g, '-')}`}\n                        className=\"text-sm text-gray-600 hover:text-black transition-colors\"\n                      >\n                        {category}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              {/* Designers */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Designers</h3>\n                <ul className=\"space-y-2\">\n                  {designers.slice(0, 10).map((designer, index) => (\n                    <li key={index}>\n                      <Link\n                        href={`/womens/designers/${designer.toLowerCase().replace(/\\s+/g, '-')}`}\n                        className=\"text-sm text-gray-600 hover:text-black transition-colors\"\n                      >\n                        {designer}\n                      </Link>\n                    </li>\n                  ))}\n                  <li>\n                    <button className=\"text-sm text-gray-600 hover:text-black transition-colors\">\n                      View all designers\n                    </button>\n                  </li>\n                </ul>\n              </div>\n\n              {/* Size Filter */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Size</h3>\n                <div className=\"grid grid-cols-3 gap-2\">\n                  {['XXS', 'XS', 'S', 'M', 'L', 'XL'].map((size) => (\n                    <button\n                      key={size}\n                      className=\"border border-gray-300 py-2 text-sm hover:border-black transition-colors\"\n                    >\n                      {size}\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Price Filter */}\n              <div>\n                <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Price</h3>\n                <div className=\"space-y-2\">\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"5000\"\n                    className=\"w-full\"\n                  />\n                  <div className=\"flex justify-between text-sm text-gray-600\">\n                    <span>£0</span>\n                    <span>£5000+</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </aside>\n\n          {/* Main Content */}\n          <main className=\"flex-1\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between mb-8\">\n              <div>\n                <h1 className=\"text-3xl font-bold mb-2\">Women's Clothing</h1>\n                <p className=\"text-gray-600\">{products.length} items</p>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                {/* Mobile Filter Toggle */}\n                <button\n                  className=\"lg:hidden flex items-center space-x-2 px-4 py-2 border border-gray-300 hover:border-black\"\n                  onClick={() => setShowFilters(!showFilters)}\n                >\n                  <Filter size={16} />\n                  <span className=\"text-sm\">Filters</span>\n                </button>\n\n                {/* Sort Dropdown */}\n                <div className=\"relative\">\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value)}\n                    className=\"appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm hover:border-black focus:outline-none focus:border-black\"\n                  >\n                    <option value=\"newest\">Newest</option>\n                    <option value=\"price-low\">Price: Low to High</option>\n                    <option value=\"price-high\">Price: High to Low</option>\n                    <option value=\"popular\">Most Popular</option>\n                  </select>\n                  <ChevronDown size={16} className=\"absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none\" />\n                </div>\n\n                {/* View Mode Toggle */}\n                <div className=\"flex border border-gray-300\">\n                  <button\n                    className={`p-2 ${viewMode === 'grid' ? 'bg-black text-white' : 'bg-white text-black'}`}\n                    onClick={() => setViewMode('grid')}\n                  >\n                    <Grid size={16} />\n                  </button>\n                  <button\n                    className={`p-2 ${viewMode === 'list' ? 'bg-black text-white' : 'bg-white text-black'}`}\n                    onClick={() => setViewMode('list')}\n                  >\n                    <List size={16} />\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Product Grid */}\n            <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>\n              {products.map((product) => (\n                <div key={product.id} className=\"group cursor-pointer\">\n                  <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 mb-4 overflow-hidden\">\n                    <div className=\"w-full h-full flex items-center justify-center\">\n                      <span className=\"text-gray-500 text-sm\">Product Image</span>\n                    </div>\n                  </div>\n                  <div className=\"space-y-1\">\n                    <p className=\"text-xs text-gray-500 uppercase tracking-wide\">{product.brand}</p>\n                    <h3 className=\"text-sm font-medium group-hover:text-gray-600\">{product.name}</h3>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-sm font-semibold\">£{product.price}</span>\n                      {product.isOnSale && (\n                        <span className=\"text-sm text-gray-500 line-through\">£{product.originalPrice}</span>\n                      )}\n                    </div>\n                    <p className=\"text-xs text-gray-500\">{product.category}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Load More */}\n            <div className=\"text-center mt-12\">\n              <button className=\"luxury-button-outline\">\n                Load More\n              </button>\n            </div>\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,oBAAoB;IACpB,MAAM,WAAW,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC;YACrD,IAAI,IAAI;YACR,MAAM,CAAC,cAAc,EAAE,IAAI,GAAG;YAC9B,OAAO;gBAAC;gBAAS;gBAAiB;gBAAkB;gBAAW;aAAS,CAAC,IAAI,EAAE;YAC/E,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YAC1C,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;YAClD,UAAU,KAAK,MAAM,KAAK;YAC1B,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG;YACzB,UAAU;gBAAC;gBAAW;gBAAS;gBAAQ;gBAAY;aAAW,CAAC,IAAI,EAAE;QACvE,CAAC;IAED,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAmB;;;;;;sCAC5C,8OAAC;4BAAK,WAAU;sCAAO;;;;;;sCACvB,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAU,WAAU;sCAAmB;;;;;;sCAClD,8OAAC;4BAAK,WAAU;sCAAO;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAa;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAM,WAAW,CAAC,UAAU,EAAE,cAAc,UAAU,SAAS,SAAS,CAAC;sCACxE,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAG,WAAU;0DACX,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;kEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,sBAAsB,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;4DAC5E,WAAU;sEAET;;;;;;uDALI;;;;;;;;;;;;;;;;kDAaf,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAG,WAAU;;oDACX,UAAU,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,sBACrC,8OAAC;sEACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,kBAAkB,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;gEACxE,WAAU;0EAET;;;;;;2DALI;;;;;kEASX,8OAAC;kEACC,cAAA,8OAAC;4DAAO,WAAU;sEAA2D;;;;;;;;;;;;;;;;;;;;;;;kDAQnF,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAO;oDAAM;oDAAK;oDAAK;oDAAK;iDAAK,CAAC,GAAG,CAAC,CAAC,qBACvC,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDAUb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,8OAAC;4BAAK,WAAU;;8CAEd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;;wDAAiB,SAAS,MAAM;wDAAC;;;;;;;;;;;;;sDAGhD,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,WAAU;oDACV,SAAS,IAAM,eAAe,CAAC;;sEAE/B,8OAAC,sMAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;sEACd,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAI5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4DACzC,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,8OAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,8OAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,8OAAC;oEAAO,OAAM;8EAAU;;;;;;;;;;;;sEAE1B,8OAAC,oNAAA,CAAA,cAAW;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;;8DAInC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,wBAAwB,uBAAuB;4DACvF,SAAS,IAAM,YAAY;sEAE3B,cAAA,8OAAC,yMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;sEAEd,8OAAC;4DACC,WAAW,CAAC,IAAI,EAAE,aAAa,SAAS,wBAAwB,uBAAuB;4DACvF,SAAS,IAAM,YAAY;sEAE3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,8OAAC;oCAAI,WAAW,CAAC,WAAW,EAAE,aAAa,SAAS,8CAA8C,eAAe;8CAC9G,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAAqB,WAAU;;8DAC9B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;8DAG5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAiD,QAAQ,KAAK;;;;;;sEAC3E,8OAAC;4DAAG,WAAU;sEAAiD,QAAQ,IAAI;;;;;;sEAC3E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;wEAAwB;wEAAE,QAAQ,KAAK;;;;;;;gEACtD,QAAQ,QAAQ,kBACf,8OAAC;oEAAK,WAAU;;wEAAqC;wEAAE,QAAQ,aAAa;;;;;;;;;;;;;sEAGhF,8OAAC;4DAAE,WAAU;sEAAyB,QAAQ,QAAQ;;;;;;;;;;;;;2CAfhD,QAAQ,EAAE;;;;;;;;;;8CAsBxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAO,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "file": "funnel.js", "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/lucide-react/src/icons/funnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "file": "grid-3x3.js", "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/lucide-react/src/icons/grid-3x3.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n  ['path', { d: 'M3 9h18', key: '1pudct' }],\n  ['path', { d: 'M3 15h18', key: '5xshup' }],\n  ['path', { d: 'M9 3v18', key: 'fh3hqa' }],\n  ['path', { d: 'M15 3v18', key: '14nvp0' }],\n];\n\n/**\n * @component @name Grid3x3\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDloMTgiIC8+CiAgPHBhdGggZD0iTTMgMTVoMTgiIC8+CiAgPHBhdGggZD0iTTkgM3YxOCIgLz4KICA8cGF0aCBkPSJNMTUgM3YxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/grid-3x3\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Grid3x3 = createLucideIcon('grid-3x3', __iconNode);\n\nexport default Grid3x3;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "file": "list.js", "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/lucide-react/src/icons/list.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12h.01', key: 'nlz23k' }],\n  ['path', { d: 'M3 18h.01', key: '1tta3j' }],\n  ['path', { d: 'M3 6h.01', key: '1rqtza' }],\n  ['path', { d: 'M8 12h13', key: '1za7za' }],\n  ['path', { d: 'M8 18h13', key: '1lx6n3' }],\n  ['path', { d: 'M8 6h13', key: 'ik3vkj' }],\n];\n\n/**\n * @component @name List\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmguMDEiIC8+CiAgPHBhdGggZD0iTTMgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik0zIDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEyaDEzIiAvPgogIDxwYXRoIGQ9Ik04IDE4aDEzIiAvPgogIDxwYXRoIGQ9Ik04IDZoMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/list\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst List = createLucideIcon('list', __iconNode);\n\nexport default List;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC1C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "file": "chevron-down.js", "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/lucide-react/src/icons/chevron-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('chevron-down', __iconNode);\n\nexport default ChevronDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,cAAgB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa7E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}