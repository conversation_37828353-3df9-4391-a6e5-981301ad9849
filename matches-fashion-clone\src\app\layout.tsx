import type { Metadata } from "next";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

export const metadata: Metadata = {
  title: "Luxury Fashion | Designer Clothing, Bags & Shoes | MATCHES UK",
  description: "Discover luxury fashion at MATCHES. Shop designer clothing, bags, shoes and accessories from over 450 established and innovative designer brands.",
  keywords: "luxury fashion, designer clothing, designer bags, designer shoes, luxury accessories, high-end fashion",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="min-h-screen flex flex-col antialiased">
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
