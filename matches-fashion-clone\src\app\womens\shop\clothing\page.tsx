'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Filter, Grid, List, ChevronDown } from 'lucide-react';

export default function WomensClothingPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('newest');
  const [showFilters, setShowFilters] = useState(false);

  // Mock product data
  const products = Array.from({ length: 24 }, (_, i) => ({
    id: i + 1,
    name: `Designer Item ${i + 1}`,
    brand: ['Gucci', 'Saint Laurent', 'Bottega Veneta', 'The Row', '<PERSON>haite'][i % 5],
    price: Math.floor(Math.random() * 2000) + 200,
    originalPrice: Math.floor(Math.random() * 2000) + 200,
    isOnSale: Math.random() > 0.7,
    image: `product-${i + 1}`,
    category: ['Dresses', 'Coats', 'Tops', 'Trousers', 'Knitwear'][i % 5],
  }));

  const categories = [
    'Shop all',
    'Activewear',
    'Beachwear',
    'Bridal',
    'Cardigans',
    'Coats',
    'Denim',
    'Dresses',
    'Jackets',
    'Jeans',
    'Jumpsuits',
    'Knitwear',
    'Lingerie and nightwear',
    'Loungewear',
    'Matching sets',
    'Skirts',
    'Suits',
    'Swimwear',
    'Tops',
    'Trousers',
  ];

  const designers = [
    'ALAÏA',
    'Alexander McQueen',
    'Balenciaga',
    'Bottega Veneta',
    'Dolce & Gabbana',
    'Erdem',
    'Gabriela Hearst',
    'Gucci',
    'Isabel Marant',
    'Jacquemus',
    'Khaite',
    'LOEWE',
    'Max Mara',
    'Moncler',
    'Saint Laurent',
    'The Row',
    'Toteme',
    'Valentino Garavani',
    'Zimmermann',
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 py-4">
        <nav className="text-sm text-gray-500">
          <Link href="/" className="hover:text-black">Home</Link>
          <span className="mx-2">/</span>
          <Link href="/womens" className="hover:text-black">Women</Link>
          <span className="mx-2">/</span>
          <span className="text-black">Clothing</span>
        </nav>
      </div>

      <div className="max-w-7xl mx-auto px-4">
        <div className="flex">
          {/* Sidebar Filters */}
          <aside className={`w-64 pr-8 ${showFilters ? 'block' : 'hidden'} lg:block`}>
            <div className="space-y-8">
              {/* Categories */}
              <div>
                <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Clothing</h3>
                <ul className="space-y-2">
                  {categories.map((category, index) => (
                    <li key={index}>
                      <Link
                        href={`/womens/shop/clothing/${category.toLowerCase().replace(/\s+/g, '-')}`}
                        className="text-sm text-gray-600 hover:text-black transition-colors"
                      >
                        {category}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Designers */}
              <div>
                <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Designers</h3>
                <ul className="space-y-2">
                  {designers.slice(0, 10).map((designer, index) => (
                    <li key={index}>
                      <Link
                        href={`/womens/designers/${designer.toLowerCase().replace(/\s+/g, '-')}`}
                        className="text-sm text-gray-600 hover:text-black transition-colors"
                      >
                        {designer}
                      </Link>
                    </li>
                  ))}
                  <li>
                    <button className="text-sm text-gray-600 hover:text-black transition-colors">
                      View all designers
                    </button>
                  </li>
                </ul>
              </div>

              {/* Size Filter */}
              <div>
                <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Size</h3>
                <div className="grid grid-cols-3 gap-2">
                  {['XXS', 'XS', 'S', 'M', 'L', 'XL'].map((size) => (
                    <button
                      key={size}
                      className="border border-gray-300 py-2 text-sm hover:border-black transition-colors"
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              {/* Price Filter */}
              <div>
                <h3 className="font-semibold text-sm uppercase tracking-wide mb-4">Price</h3>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="5000"
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>£0</span>
                    <span>£5000+</span>
                  </div>
                </div>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <main className="flex-1">
            {/* Header */}
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold mb-2">Women's Clothing</h1>
                <p className="text-gray-600">{products.length} items</p>
              </div>

              <div className="flex items-center space-x-4">
                {/* Mobile Filter Toggle */}
                <button
                  className="lg:hidden flex items-center space-x-2 px-4 py-2 border border-gray-300 hover:border-black"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter size={16} />
                  <span className="text-sm">Filters</span>
                </button>

                {/* Sort Dropdown */}
                <div className="relative">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm hover:border-black focus:outline-none focus:border-black"
                  >
                    <option value="newest">Newest</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="popular">Most Popular</option>
                  </select>
                  <ChevronDown size={16} className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                </div>

                {/* View Mode Toggle */}
                <div className="flex border border-gray-300">
                  <button
                    className={`p-2 ${viewMode === 'grid' ? 'bg-black text-white' : 'bg-white text-black'}`}
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid size={16} />
                  </button>
                  <button
                    className={`p-2 ${viewMode === 'list' ? 'bg-black text-white' : 'bg-white text-black'}`}
                    onClick={() => setViewMode('list')}
                  >
                    <List size={16} />
                  </button>
                </div>
              </div>
            </div>

            {/* Product Grid */}
            <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
              {products.map((product) => (
                <div key={product.id} className="group cursor-pointer">
                  <div className="aspect-[3/4] bg-gradient-to-br from-gray-100 to-gray-200 mb-4 overflow-hidden">
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-gray-500 text-sm">Product Image</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-gray-500 uppercase tracking-wide">{product.brand}</p>
                    <h3 className="text-sm font-medium group-hover:text-gray-600">{product.name}</h3>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-semibold">£{product.price}</span>
                      {product.isOnSale && (
                        <span className="text-sm text-gray-500 line-through">£{product.originalPrice}</span>
                      )}
                    </div>
                    <p className="text-xs text-gray-500">{product.category}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center mt-12">
              <button className="luxury-button-outline">
                Load More
              </button>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
