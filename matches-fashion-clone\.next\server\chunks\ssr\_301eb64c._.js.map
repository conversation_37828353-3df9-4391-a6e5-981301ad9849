{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/app/womens/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\n\nexport default function WomensPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"relative h-screen flex items-center justify-center\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-300\"></div>\n        <div className=\"relative z-10 text-center text-black\">\n          <p className=\"text-sm uppercase tracking-wide mb-4\">introducing the new collections</p>\n          <h1 className=\"text-6xl md:text-8xl font-bold mb-6\">24/7 Style</h1>\n          <button className=\"luxury-button\">SHOP</button>\n        </div>\n      </section>\n\n      {/* Navigation Bar */}\n      <nav className=\"bg-white border-b border-gray-200 sticky top-0 z-40\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"flex justify-center space-x-8 py-4\">\n            <Link href=\"/womens/designers\" className=\"text-sm font-medium uppercase tracking-wide hover:text-gray-600\">\n              Designers\n            </Link>\n            <Link href=\"/womens/shop/clothing\" className=\"text-sm font-medium uppercase tracking-wide hover:text-gray-600\">\n              Clothing\n            </Link>\n            <Link href=\"/womens/shop/shoes\" className=\"text-sm font-medium uppercase tracking-wide hover:text-gray-600\">\n              Shoes\n            </Link>\n            <Link href=\"/womens/shop/bags\" className=\"text-sm font-medium uppercase tracking-wide hover:text-gray-600\">\n              Bags\n            </Link>\n            <Link href=\"/womens/shop/accessories\" className=\"text-sm font-medium uppercase tracking-wide hover:text-gray-600\">\n              Accessories\n            </Link>\n            <Link href=\"/womens/shop/jewellery-and-watches\" className=\"text-sm font-medium uppercase tracking-wide hover:text-gray-600\">\n              Fine Jewellery\n            </Link>\n            <Link href=\"/womens/shop/homeware\" className=\"text-sm font-medium uppercase tracking-wide hover:text-gray-600\">\n              Home\n            </Link>\n            <Link href=\"/womens/stories\" className=\"text-sm font-medium uppercase tracking-wide hover:text-gray-600\">\n              Stories\n            </Link>\n          </div>\n        </div>\n      </nav>\n\n      {/* Featured Content Grid */}\n      <section className=\"max-w-7xl mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {/* Featured Item 1 */}\n          <Link href=\"/womens/shop/clothing/coats\" className=\"group\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden\">\n              <div className=\"w-full h-full flex items-center justify-center\">\n                <span className=\"text-gray-500\">Coat Image</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <p className=\"text-xs uppercase tracking-wide text-gray-500\">in focus: gucci</p>\n              <h3 className=\"text-lg font-medium\">Most-Coveted Coats</h3>\n              <button className=\"text-sm font-medium uppercase tracking-wide\">SHOP</button>\n            </div>\n          </Link>\n\n          {/* Featured Item 2 */}\n          <Link href=\"/womens/shop/shoes/heels\" className=\"group\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden\">\n              <div className=\"w-full h-full flex items-center justify-center\">\n                <span className=\"text-gray-500\">Heels Image</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <p className=\"text-xs uppercase tracking-wide text-gray-500\">featuring khaite</p>\n              <h3 className=\"text-lg font-medium\">Midas Touch: Kitten Heels</h3>\n              <button className=\"text-sm font-medium uppercase tracking-wide\">SHOP</button>\n            </div>\n          </Link>\n\n          {/* Featured Item 3 */}\n          <Link href=\"/womens/designers/gabriela-hearst\" className=\"group\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden\">\n              <div className=\"w-full h-full flex items-center justify-center\">\n                <span className=\"text-gray-500\">Tailoring Image</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <p className=\"text-xs uppercase tracking-wide text-gray-500\">tonal grey tailoring</p>\n              <h3 className=\"text-lg font-medium\">Gabriela Hearst</h3>\n              <button className=\"text-sm font-medium uppercase tracking-wide\">SHOP</button>\n            </div>\n          </Link>\n\n          {/* Featured Item 4 */}\n          <Link href=\"/womens/lists/new-season-heros/bags\" className=\"group\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden\">\n              <div className=\"w-full h-full flex items-center justify-center\">\n                <span className=\"text-gray-500\">Bags Image</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <p className=\"text-xs uppercase tracking-wide text-gray-500\">Simone rocha & more</p>\n              <h3 className=\"text-lg font-medium\">Wishlist-Worthy Bags</h3>\n              <button className=\"text-sm font-medium uppercase tracking-wide\">SHOP</button>\n            </div>\n          </Link>\n\n          {/* Featured Item 5 */}\n          <Link href=\"/womens/lists/trending\" className=\"group\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden\">\n              <div className=\"w-full h-full flex items-center justify-center\">\n                <span className=\"text-gray-500\">Trending Image</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <p className=\"text-xs uppercase tracking-wide text-gray-500\">including 16arlington</p>\n              <h3 className=\"text-lg font-medium\">Trending Designers</h3>\n              <button className=\"text-sm font-medium uppercase tracking-wide\">SHOP</button>\n            </div>\n          </Link>\n\n          {/* Featured Item 6 */}\n          <Link href=\"/womens/lists/for-skiing\" className=\"group\">\n            <div className=\"aspect-[3/4] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden\">\n              <div className=\"w-full h-full flex items-center justify-center\">\n                <span className=\"text-gray-500\">Ski Image</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <p className=\"text-xs uppercase tracking-wide text-gray-500\">slope style</p>\n              <h3 className=\"text-lg font-medium\">Ski: Fashion Meets Function</h3>\n              <button className=\"text-sm font-medium uppercase tracking-wide\">SHOP</button>\n            </div>\n          </Link>\n        </div>\n\n        {/* Large Featured Items */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mt-12\">\n          <Link href=\"/womens/lists/for-skiing\" className=\"group\">\n            <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden\">\n              <div className=\"w-full h-full flex items-center justify-center\">\n                <span className=\"text-gray-500\">Large Ski Image</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <p className=\"text-xs uppercase tracking-wide text-gray-500\">slope style</p>\n              <h3 className=\"text-xl font-medium\">Ski: Fashion Meets Function</h3>\n              <button className=\"text-sm font-medium uppercase tracking-wide\">SHOP</button>\n            </div>\n          </Link>\n\n          <Link href=\"/stories/2024/02/fashion-valentines-day-gifting-dressing\" className=\"group\">\n            <div className=\"aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300 mb-4 overflow-hidden\">\n              <div className=\"w-full h-full flex items-center justify-center\">\n                <span className=\"text-gray-500\">Valentine's Image</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <p className=\"text-xs uppercase tracking-wide text-gray-500\">curated by matches</p>\n              <h3 className=\"text-xl font-medium\">The Valentine's Day Edit</h3>\n              <button className=\"text-sm font-medium uppercase tracking-wide\">read and SHOP</button>\n            </div>\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CACpD,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAO,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAKtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAoB,WAAU;0CAAkE;;;;;;0CAG3G,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAwB,WAAU;0CAAkE;;;;;;0CAG/G,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,WAAU;0CAAkE;;;;;;0CAG5G,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAoB,WAAU;0CAAkE;;;;;;0CAG3G,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA2B,WAAU;0CAAkE;;;;;;0CAGlH,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqC,WAAU;0CAAkE;;;;;;0CAG5H,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAwB,WAAU;0CAAkE;;;;;;0CAG/G,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAkB,WAAU;0CAAkE;;;;;;;;;;;;;;;;;;;;;;0BAQ/G,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA8B,WAAU;;kDACjD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAO,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAKpE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA2B,WAAU;;kDAC9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAO,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAKpE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAoC,WAAU;;kDACvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAO,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAKpE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAsC,WAAU;;kDACzD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAO,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAKpE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAyB,WAAU;;kDAC5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAO,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAKpE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA2B,WAAU;;kDAC9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAO,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA2B,WAAU;;kDAC9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAO,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;0CAIpE,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA2D,WAAU;;kDAC9E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;kDAGpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAO,WAAU;0DAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9E", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,KAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}