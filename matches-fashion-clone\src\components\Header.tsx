'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Menu, X, Search, User, Settings, Heart, ShoppingBag } from 'lucide-react';
import MegaMenu from './MegaMenu';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuHover = (menu: string) => {
    setActiveMenu(menu);
  };

  const handleMenuLeave = () => {
    setActiveMenu(null);
  };

  return (
    <header className="relative bg-white border-b border-gray-200">
      {/* Top Bar - Hidden on mobile */}
      <div className="hidden md:block bg-black text-white text-xs py-2">
        <div className="max-w-7xl mx-auto px-4 flex justify-between items-center">
          <div className="flex space-x-6">
            <Link href="/help" className="hover:underline">Help Centre</Link>
            <Link href="/delivery" className="hover:underline">Delivery</Link>
            <Link href="/returns" className="hover:underline">Returns</Link>
          </div>
          <div className="flex space-x-6">
            <Link href="/stores" className="hover:underline">Visit Us</Link>
            <Link href="/apps" className="hover:underline">Our Apps</Link>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>

          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <span className="text-2xl md:text-3xl font-bold tracking-wider">MATCHES</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <div
              className="relative"
              onMouseEnter={() => handleMenuHover('women')}
              onMouseLeave={handleMenuLeave}
            >
              <Link
                href="/womens"
                className="text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Women
              </Link>
              {activeMenu === 'women' && <MegaMenu type="women" />}
            </div>
            <div
              className="relative"
              onMouseEnter={() => handleMenuHover('men')}
              onMouseLeave={handleMenuLeave}
            >
              <Link
                href="/mens"
                className="text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors"
              >
                Men
              </Link>
              {activeMenu === 'men' && <MegaMenu type="men" />}
            </div>
          </nav>

          {/* Right Icons */}
          <div className="flex items-center space-x-4">
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
              <Search size={20} />
            </button>
            <button className="hidden md:block p-2 hover:bg-gray-100 rounded-full transition-colors">
              <User size={20} />
            </button>
            <button className="hidden md:block p-2 hover:bg-gray-100 rounded-full transition-colors">
              <Settings size={20} />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors relative">
              <Heart size={20} />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                0
              </span>
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-full transition-colors relative">
              <ShoppingBag size={20} />
              <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                0
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={toggleMobileMenu} />
          <div className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl">
            <div className="flex items-center justify-between p-4 border-b">
              <span className="text-xl font-bold">MATCHES</span>
              <button onClick={toggleMobileMenu}>
                <X size={24} />
              </button>
            </div>
            <nav className="p-4">
              <div className="space-y-4">
                <Link
                  href="/womens"
                  className="block text-lg font-medium py-2 border-b border-gray-100"
                  onClick={toggleMobileMenu}
                >
                  Women
                </Link>
                <Link
                  href="/mens"
                  className="block text-lg font-medium py-2 border-b border-gray-100"
                  onClick={toggleMobileMenu}
                >
                  Men
                </Link>
                <div className="pt-4 space-y-2">
                  <Link href="/account" className="block text-sm py-1">My Account</Link>
                  <Link href="/settings" className="block text-sm py-1">Settings</Link>
                  <Link href="/help" className="block text-sm py-1">Help Centre</Link>
                  <Link href="/delivery" className="block text-sm py-1">Delivery</Link>
                </div>
              </div>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
