{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/MegaMenu.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\n\ninterface MegaMenuProps {\n  type: 'women' | 'men';\n}\n\nconst MegaMenu = ({ type }: MegaMenuProps) => {\n  const womenCategories = [\n    {\n      title: 'Just In',\n      links: [\n        { name: 'Just in this month', href: '/womens/just-in/just-in-this-month' },\n        { name: 'Just in 7 days', href: '/womens/just-in/just-in-last-7-days' },\n        { name: 'Back in stock', href: '/womens/lists/back-in-stock' },\n        { name: 'Coming soon', href: '/womens/lists/coming-soon' },\n        { name: 'Exclusives', href: '/womens/lists/exclusives' },\n      ]\n    },\n    {\n      title: 'Clothing',\n      links: [\n        { name: 'Shop all', href: '/womens/shop/clothing' },\n        { name: 'Dresses', href: '/womens/shop/clothing/dresses' },\n        { name: 'Coats', href: '/womens/shop/clothing/coats' },\n        { name: 'Jackets', href: '/womens/shop/clothing/jackets' },\n        { name: 'Knitwear', href: '/womens/shop/clothing/knitwear' },\n        { name: 'Tops', href: '/womens/shop/clothing/tops' },\n        { name: 'Trousers', href: '/womens/shop/clothing/trousers' },\n        { name: 'Jeans', href: '/womens/shop/clothing/jeans' },\n        { name: 'Skirts', href: '/womens/shop/clothing/skirts' },\n      ]\n    },\n    {\n      title: 'Shoes',\n      links: [\n        { name: 'Shop all', href: '/womens/shop/shoes' },\n        { name: 'Boots', href: '/womens/shop/shoes/boots' },\n        { name: 'Heels', href: '/womens/shop/shoes/heels' },\n        { name: 'Flats', href: '/womens/shop/shoes/flats' },\n        { name: 'Trainers', href: '/womens/shop/shoes/sneakers' },\n        { name: 'Sandals', href: '/womens/shop/shoes/sandals' },\n      ]\n    },\n    {\n      title: 'Bags',\n      links: [\n        { name: 'Shop all', href: '/womens/shop/bags' },\n        { name: 'Tote bags', href: '/womens/shop/bags/tote-bags' },\n        { name: 'Shoulder bags', href: '/womens/shop/bags/shoulder-bags' },\n        { name: 'Cross-body bags', href: '/womens/shop/bags/cross-body-bags' },\n        { name: 'Clutch bags', href: '/womens/shop/bags/clutch-bags' },\n        { name: 'Mini bags', href: '/womens/shop/bags/mini-bags' },\n      ]\n    },\n    {\n      title: 'Accessories',\n      links: [\n        { name: 'View all', href: '/womens/shop/accessories' },\n        { name: 'Jewellery', href: '/womens/shop/jewellery-and-watches' },\n        { name: 'Sunglasses', href: '/womens/shop/accessories/sunglasses' },\n        { name: 'Belts', href: '/womens/shop/accessories/belts' },\n        { name: 'Scarves', href: '/womens/shop/accessories/scarves' },\n        { name: 'Hats', href: '/womens/shop/accessories/hats' },\n      ]\n    }\n  ];\n\n  const menCategories = [\n    {\n      title: 'Just In',\n      links: [\n        { name: 'Just in this month', href: '/mens/just-in/just-in-this-month' },\n        { name: 'Just in 7 days', href: '/mens/just-in/just-in-last-7-days' },\n        { name: 'Back in stock', href: '/mens/lists/back-in-stock' },\n        { name: 'Coming soon', href: '/mens/lists/coming-soon' },\n        { name: 'Exclusives', href: '/mens/lists/exclusives' },\n      ]\n    },\n    {\n      title: 'Clothing',\n      links: [\n        { name: 'Shop all', href: '/mens/shop/clothing' },\n        { name: 'Suits', href: '/mens/shop/clothing/suits' },\n        { name: 'Blazers', href: '/mens/shop/clothing/blazers' },\n        { name: 'Coats', href: '/mens/shop/clothing/coats' },\n        { name: 'Knitwear', href: '/mens/shop/clothing/knitwear' },\n        { name: 'Shirts', href: '/mens/shop/clothing/casual-shirts' },\n        { name: 'T-shirts', href: '/mens/shop/clothing/t-shirts' },\n        { name: 'Trousers', href: '/mens/shop/clothing/trousers' },\n        { name: 'Jeans', href: '/mens/shop/clothing/jeans' },\n      ]\n    },\n    {\n      title: 'Shoes',\n      links: [\n        { name: 'Shop all', href: '/mens/shop/shoes' },\n        { name: 'Formal shoes', href: '/mens/shop/shoes/dress-shoes' },\n        { name: 'Boots', href: '/mens/shop/shoes/boots' },\n        { name: 'Trainers', href: '/mens/shop/shoes/sneakers' },\n        { name: 'Loafers', href: '/mens/shop/shoes/loafers' },\n      ]\n    },\n    {\n      title: 'Bags',\n      links: [\n        { name: 'Shop all', href: '/mens/shop/bags' },\n        { name: 'Backpacks', href: '/mens/shop/bags/backpacks' },\n        { name: 'Travel bags', href: '/mens/shop/bags/travel-bags' },\n        { name: 'Briefcases', href: '/mens/shop/bags/briefcases' },\n        { name: 'Messenger bags', href: '/mens/shop/bags/messenger-bags' },\n      ]\n    },\n    {\n      title: 'Accessories',\n      links: [\n        { name: 'View all', href: '/mens/shop/accessories' },\n        { name: 'Watches', href: '/mens/shop/accessories/jewellery/watches' },\n        { name: 'Sunglasses', href: '/mens/shop/accessories/sunglasses' },\n        { name: 'Belts', href: '/mens/shop/accessories/belts' },\n        { name: 'Wallets', href: '/mens/shop/accessories/wallets' },\n        { name: 'Ties', href: '/mens/shop/accessories/ties' },\n      ]\n    }\n  ];\n\n  const categories = type === 'women' ? womenCategories : menCategories;\n\n  const featuredDesigners = type === 'women' \n    ? ['Gucci', 'Saint Laurent', 'Bottega Veneta', 'The Row', 'Khaite', 'Toteme']\n    : ['Tom Ford', 'Brunello Cucinelli', 'Stone Island', 'Thom Browne', 'Bottega Veneta', 'Gucci'];\n\n  return (\n    <div className=\"absolute top-full left-0 w-screen bg-white shadow-xl border-t border-gray-200 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 py-8\">\n        <div className=\"grid grid-cols-6 gap-8\">\n          {/* Categories */}\n          {categories.map((category, index) => (\n            <div key={index} className=\"space-y-4\">\n              <h3 className=\"font-semibold text-sm uppercase tracking-wide text-gray-900\">\n                {category.title}\n              </h3>\n              <ul className=\"space-y-2\">\n                {category.links.map((link, linkIndex) => (\n                  <li key={linkIndex}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-gray-600 hover:text-black transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n\n          {/* Featured Designers */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide text-gray-900\">\n              Featured Designers\n            </h3>\n            <ul className=\"space-y-2\">\n              {featuredDesigners.map((designer, index) => (\n                <li key={index}>\n                  <Link\n                    href={`/${type}/designers/${designer.toLowerCase().replace(/\\s+/g, '-')}`}\n                    className=\"text-sm text-gray-600 hover:text-black transition-colors\"\n                  >\n                    {designer}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Featured Products Section */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"grid grid-cols-4 gap-6\">\n            {[1, 2, 3, 4].map((item) => (\n              <div key={item} className=\"group cursor-pointer\">\n                <div className=\"aspect-square bg-gray-100 mb-3 overflow-hidden\">\n                  <div className=\"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center\">\n                    <span className=\"text-gray-500 text-sm\">Product Image</span>\n                  </div>\n                </div>\n                <div className=\"space-y-1\">\n                  <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Designer Name</p>\n                  <p className=\"text-sm font-medium\">Product Name</p>\n                  <p className=\"text-sm font-semibold\">£XXX</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MegaMenu;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAiB;IACvC,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAAqC;gBACzE;oBAAE,MAAM;oBAAkB,MAAM;gBAAsC;gBACtE;oBAAE,MAAM;oBAAiB,MAAM;gBAA8B;gBAC7D;oBAAE,MAAM;oBAAe,MAAM;gBAA4B;gBACzD;oBAAE,MAAM;oBAAc,MAAM;gBAA2B;aACxD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAwB;gBAClD;oBAAE,MAAM;oBAAW,MAAM;gBAAgC;gBACzD;oBAAE,MAAM;oBAAS,MAAM;gBAA8B;gBACrD;oBAAE,MAAM;oBAAW,MAAM;gBAAgC;gBACzD;oBAAE,MAAM;oBAAY,MAAM;gBAAiC;gBAC3D;oBAAE,MAAM;oBAAQ,MAAM;gBAA6B;gBACnD;oBAAE,MAAM;oBAAY,MAAM;gBAAiC;gBAC3D;oBAAE,MAAM;oBAAS,MAAM;gBAA8B;gBACrD;oBAAE,MAAM;oBAAU,MAAM;gBAA+B;aACxD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAqB;gBAC/C;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAS,MAAM;gBAA2B;gBAClD;oBAAE,MAAM;oBAAY,MAAM;gBAA8B;gBACxD;oBAAE,MAAM;oBAAW,MAAM;gBAA6B;aACvD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAoB;gBAC9C;oBAAE,MAAM;oBAAa,MAAM;gBAA8B;gBACzD;oBAAE,MAAM;oBAAiB,MAAM;gBAAkC;gBACjE;oBAAE,MAAM;oBAAmB,MAAM;gBAAoC;gBACrE;oBAAE,MAAM;oBAAe,MAAM;gBAAgC;gBAC7D;oBAAE,MAAM;oBAAa,MAAM;gBAA8B;aAC1D;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAA2B;gBACrD;oBAAE,MAAM;oBAAa,MAAM;gBAAqC;gBAChE;oBAAE,MAAM;oBAAc,MAAM;gBAAsC;gBAClE;oBAAE,MAAM;oBAAS,MAAM;gBAAiC;gBACxD;oBAAE,MAAM;oBAAW,MAAM;gBAAmC;gBAC5D;oBAAE,MAAM;oBAAQ,MAAM;gBAAgC;aACvD;QACH;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAAmC;gBACvE;oBAAE,MAAM;oBAAkB,MAAM;gBAAoC;gBACpE;oBAAE,MAAM;oBAAiB,MAAM;gBAA4B;gBAC3D;oBAAE,MAAM;oBAAe,MAAM;gBAA0B;gBACvD;oBAAE,MAAM;oBAAc,MAAM;gBAAyB;aACtD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAsB;gBAChD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;gBACnD;oBAAE,MAAM;oBAAW,MAAM;gBAA8B;gBACvD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;gBACnD;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAU,MAAM;gBAAoC;gBAC5D;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAY,MAAM;gBAA+B;gBACzD;oBAAE,MAAM;oBAAS,MAAM;gBAA4B;aACpD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAmB;gBAC7C;oBAAE,MAAM;oBAAgB,MAAM;gBAA+B;gBAC7D;oBAAE,MAAM;oBAAS,MAAM;gBAAyB;gBAChD;oBAAE,MAAM;oBAAY,MAAM;gBAA4B;gBACtD;oBAAE,MAAM;oBAAW,MAAM;gBAA2B;aACrD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAkB;gBAC5C;oBAAE,MAAM;oBAAa,MAAM;gBAA4B;gBACvD;oBAAE,MAAM;oBAAe,MAAM;gBAA8B;gBAC3D;oBAAE,MAAM;oBAAc,MAAM;gBAA6B;gBACzD;oBAAE,MAAM;oBAAkB,MAAM;gBAAiC;aAClE;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAyB;gBACnD;oBAAE,MAAM;oBAAW,MAAM;gBAA2C;gBACpE;oBAAE,MAAM;oBAAc,MAAM;gBAAoC;gBAChE;oBAAE,MAAM;oBAAS,MAAM;gBAA+B;gBACtD;oBAAE,MAAM;oBAAW,MAAM;gBAAiC;gBAC1D;oBAAE,MAAM;oBAAQ,MAAM;gBAA8B;aACrD;QACH;KACD;IAED,MAAM,aAAa,SAAS,UAAU,kBAAkB;IAExD,MAAM,oBAAoB,SAAS,UAC/B;QAAC;QAAS;QAAiB;QAAkB;QAAW;QAAU;KAAS,GAC3E;QAAC;QAAY;QAAsB;QAAgB;QAAe;QAAkB;KAAQ;IAEhG,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;wBAEZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAG,WAAU;kDACX,SAAS,KAAK;;;;;;kDAEjB,8OAAC;wCAAG,WAAU;kDACX,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACzB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL;;;;;;;;;;;+BANL;;;;;sCAoBZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAG,WAAU;8CACX,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;gDACzE,WAAU;0DAET;;;;;;2CALI;;;;;;;;;;;;;;;;;;;;;;8BAcjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;gCAAe,WAAU;;kDACxB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAC7D,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;+BAT/B;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBxB;uCAEe", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport <PERSON> from 'next/link';\nimport { Menu, X, Search, User, Settings, Heart, ShoppingBag } from 'lucide-react';\nimport MegaMenu from './MegaMenu';\nimport SearchModal from './SearchModal';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeMenu, setActiveMenu] = useState<string | null>(null);\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  const handleMenuHover = (menu: string) => {\n    setActiveMenu(menu);\n  };\n\n  const handleMenuLeave = () => {\n    setActiveMenu(null);\n  };\n\n  return (\n    <header className=\"relative bg-white border-b border-gray-200\">\n      {/* Top Bar - Hidden on mobile */}\n      <div className=\"hidden md:block bg-black text-white text-xs py-2\">\n        <div className=\"max-w-7xl mx-auto px-4 flex justify-between items-center\">\n          <div className=\"flex space-x-6\">\n            <Link href=\"/help\" className=\"hover:underline\">Help Centre</Link>\n            <Link href=\"/delivery\" className=\"hover:underline\">Delivery</Link>\n            <Link href=\"/returns\" className=\"hover:underline\">Returns</Link>\n          </div>\n          <div className=\"flex space-x-6\">\n            <Link href=\"/stores\" className=\"hover:underline\">Visit Us</Link>\n            <Link href=\"/apps\" className=\"hover:underline\">Our Apps</Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Header */}\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2\"\n            onClick={toggleMobileMenu}\n            aria-label=\"Toggle menu\"\n          >\n            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}\n          </button>\n\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex-shrink-0\">\n            <span className=\"text-2xl md:text-3xl font-bold tracking-wider\">MATCHES</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <div\n              className=\"relative\"\n              onMouseEnter={() => handleMenuHover('women')}\n              onMouseLeave={handleMenuLeave}\n            >\n              <Link\n                href=\"/womens\"\n                className=\"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Women\n              </Link>\n              {activeMenu === 'women' && <MegaMenu type=\"women\" />}\n            </div>\n            <div\n              className=\"relative\"\n              onMouseEnter={() => handleMenuHover('men')}\n              onMouseLeave={handleMenuLeave}\n            >\n              <Link\n                href=\"/mens\"\n                className=\"text-sm font-medium tracking-wide uppercase hover:text-gray-600 transition-colors\"\n              >\n                Men\n              </Link>\n              {activeMenu === 'men' && <MegaMenu type=\"men\" />}\n            </div>\n          </nav>\n\n          {/* Right Icons */}\n          <div className=\"flex items-center space-x-4\">\n            <button\n              className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\n              onClick={() => setIsSearchOpen(true)}\n            >\n              <Search size={20} />\n            </button>\n            <button className=\"hidden md:block p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <User size={20} />\n            </button>\n            <button className=\"hidden md:block p-2 hover:bg-gray-100 rounded-full transition-colors\">\n              <Settings size={20} />\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors relative\">\n              <Heart size={20} />\n              <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                0\n              </span>\n            </button>\n            <button className=\"p-2 hover:bg-gray-100 rounded-full transition-colors relative\">\n              <ShoppingBag size={20} />\n              <span className=\"absolute -top-1 -right-1 bg-black text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n                0\n              </span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu Overlay */}\n      {isMobileMenuOpen && (\n        <div className=\"fixed inset-0 z-50 md:hidden\">\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" onClick={toggleMobileMenu} />\n          <div className=\"fixed left-0 top-0 h-full w-80 bg-white shadow-xl\">\n            <div className=\"flex items-center justify-between p-4 border-b\">\n              <span className=\"text-xl font-bold\">MATCHES</span>\n              <button onClick={toggleMobileMenu}>\n                <X size={24} />\n              </button>\n            </div>\n            <nav className=\"p-4\">\n              <div className=\"space-y-4\">\n                <Link\n                  href=\"/womens\"\n                  className=\"block text-lg font-medium py-2 border-b border-gray-100\"\n                  onClick={toggleMobileMenu}\n                >\n                  Women\n                </Link>\n                <Link\n                  href=\"/mens\"\n                  className=\"block text-lg font-medium py-2 border-b border-gray-100\"\n                  onClick={toggleMobileMenu}\n                >\n                  Men\n                </Link>\n                <div className=\"pt-4 space-y-2\">\n                  <Link href=\"/account\" className=\"block text-sm py-1\">My Account</Link>\n                  <Link href=\"/settings\" className=\"block text-sm py-1\">Settings</Link>\n                  <Link href=\"/help\" className=\"block text-sm py-1\">Help Centre</Link>\n                  <Link href=\"/delivery\" className=\"block text-sm py-1\">Delivery</Link>\n                </div>\n              </div>\n            </nav>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAQA,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,MAAM,kBAAkB,CAAC;QACvB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAkB;;;;;;8CACnD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAkB;;;;;;;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAkB;;;;;;8CACjD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAMrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;sCAEV,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;qDAAS,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;sCAIpD,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC;gCAAK,WAAU;0CAAgD;;;;;;;;;;;sCAIlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,cAAc,IAAM,gBAAgB;oCACpC,cAAc;;sDAEd,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;wCAGA,eAAe,yBAAW,8OAAC,8HAAA,CAAA,UAAQ;4CAAC,MAAK;;;;;;;;;;;;8CAE5C,8OAAC;oCACC,WAAU;oCACV,cAAc,IAAM,gBAAgB;oCACpC,cAAc;;sDAEd,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;wCAGA,eAAe,uBAAS,8OAAC,8HAAA,CAAA,UAAQ;4CAAC,MAAK;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,gBAAgB;8CAE/B,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;8CAEhB,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;;;;;;8CAElB,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;sDACb,8OAAC;4CAAK,WAAU;sDAA+G;;;;;;;;;;;;8CAIjI,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASpI,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAuC,SAAS;;;;;;kCAC/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;kDACpC,8OAAC;wCAAO,SAAS;kDACf,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAGb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS;sDACV;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS;sDACV;;;;;;sDAGD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAAqB;;;;;;8DACrD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAqB;;;;;;8DACtD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;8DAAqB;;;;;;8DAClD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;uCAEe", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Instagram, Facebook, Youtube, Twitter } from 'lucide-react';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-white border-t border-gray-200\">\n      {/* Newsletter Section */}\n      <div className=\"bg-gray-50 py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Stay in the know</h2>\n          <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">\n            Be the first to discover new arrivals, exclusive collections, and styling tips\n          </p>\n          <div className=\"flex max-w-md mx-auto\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email address\"\n              className=\"flex-1 px-4 py-3 border border-gray-300 focus:outline-none focus:border-black\"\n            />\n            <button className=\"luxury-button\">\n              Sign up\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* MATCHES */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">MATCHES</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/bio\" className=\"text-sm text-gray-600 hover:text-black\">About Us</Link></li>\n              <li><Link href=\"/careers\" className=\"text-sm text-gray-600 hover:text-black\">Careers</Link></li>\n              <li><Link href=\"/affiliates\" className=\"text-sm text-gray-600 hover:text-black\">Affiliates</Link></li>\n              <li><Link href=\"/press\" className=\"text-sm text-gray-600 hover:text-black\">Press</Link></li>\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Services</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/private-shopping\" className=\"text-sm text-gray-600 hover:text-black\">Private Shopping</Link></li>\n              <li><Link href=\"/loyalty\" className=\"text-sm text-gray-600 hover:text-black\">Loyalty</Link></li>\n              <li><Link href=\"/rental\" className=\"text-sm text-gray-600 hover:text-black\">MATCHES Rental</Link></li>\n              <li><Link href=\"/gift-cards\" className=\"text-sm text-gray-600 hover:text-black\">Gift Cards</Link></li>\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Legal</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/terms\" className=\"text-sm text-gray-600 hover:text-black\">Terms and Conditions</Link></li>\n              <li><Link href=\"/privacy\" className=\"text-sm text-gray-600 hover:text-black\">Privacy Policy</Link></li>\n              <li><Link href=\"/cookies\" className=\"text-sm text-gray-600 hover:text-black\">Cookie Policy</Link></li>\n              <li><Link href=\"/modern-slavery\" className=\"text-sm text-gray-600 hover:text-black\">Modern Slavery Statement</Link></li>\n            </ul>\n          </div>\n\n          {/* Visit Us */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Visit Us</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/stores/5carlosplace\" className=\"text-sm text-gray-600 hover:text-black\">5 Carlos Place</Link></li>\n              <li><Link href=\"/stores/marylebone\" className=\"text-sm text-gray-600 hover:text-black\">Marylebone</Link></li>\n              <li><Link href=\"/stores/wimbledon\" className=\"text-sm text-gray-600 hover:text-black\">Wimbledon</Link></li>\n            </ul>\n          </div>\n\n          {/* Help Centre */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Help Centre</h3>\n            <ul className=\"space-y-2\">\n              <li><Link href=\"/help\" className=\"text-sm text-gray-600 hover:text-black\">Help Centre</Link></li>\n              <li><Link href=\"/returns\" className=\"text-sm text-gray-600 hover:text-black\">Returning an item</Link></li>\n              <li><Link href=\"/delivery\" className=\"text-sm text-gray-600 hover:text-black\">Delivery</Link></li>\n              <li><Link href=\"/size-guide\" className=\"text-sm text-gray-600 hover:text-black\">Size Guide</Link></li>\n            </ul>\n          </div>\n\n          {/* Social Media & Apps */}\n          <div className=\"lg:col-span-1\">\n            <h3 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Follow Us</h3>\n            <div className=\"flex space-x-4 mb-6\">\n              <Link href=\"https://instagram.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Instagram size={20} />\n              </Link>\n              <Link href=\"https://facebook.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Facebook size={20} />\n              </Link>\n              <Link href=\"https://youtube.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Youtube size={20} />\n              </Link>\n              <Link href=\"https://twitter.com/matches\" className=\"text-gray-600 hover:text-black\">\n                <Twitter size={20} />\n              </Link>\n            </div>\n            <h4 className=\"font-semibold text-sm uppercase tracking-wide mb-4\">Our Apps</h4>\n            <div className=\"space-y-2\">\n              <Link href=\"#\" className=\"block\">\n                <div className=\"bg-black text-white px-3 py-2 text-xs rounded\">\n                  Download on the App Store\n                </div>\n              </Link>\n              <Link href=\"#\" className=\"block\">\n                <div className=\"bg-black text-white px-3 py-2 text-xs rounded\">\n                  Get it on Google Play\n                </div>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-gray-200 py-6\">\n        <div className=\"max-w-7xl mx-auto px-4 flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"flex items-center space-x-4 mb-4 md:mb-0\">\n            <span className=\"text-sm text-gray-600\">Shipping to</span>\n            <button className=\"text-sm font-medium border border-gray-300 px-3 py-1 hover:border-black\">\n              United Kingdom\n            </button>\n          </div>\n          <div className=\"text-sm text-gray-600\">\n            © Copyright 2024 MATCHES\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;sCAGnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAyC;;;;;;;;;;;sDACzE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;sDAChF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK/E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAyC;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAyC;;;;;;;;;;;sDAC5E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyC;;;;;;;;;;;sDAC3E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAAyC;;;;;;;;;;;sDACzF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DAAyC;;;;;;;;;;;sDACvF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAK1F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAyC;;;;;;;;;;;sDAC1E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyC;;;;;;;;;;;sDAC7E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAyC;;;;;;;;;;;sDAC9E,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAc,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgC,WAAU;sDACnD,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAEnB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA+B,WAAU;sDAClD,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;;;;;;sDAElB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA8B,WAAU;sDACjD,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;sDAEjB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAA8B,WAAU;sDACjD,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;;;;;;;;;;;;8CAGnB,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;sDAIjE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAO,WAAU;8CAA0E;;;;;;;;;;;;sCAI9F,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOjD;uCAEe", "debugId": null}}]}