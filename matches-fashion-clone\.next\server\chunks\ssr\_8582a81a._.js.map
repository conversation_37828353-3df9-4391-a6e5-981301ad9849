{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/src/app/returns/page.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function ReturnsPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Breadcrumb */}\n      <div className=\"max-w-7xl mx-auto px-4 py-4\">\n        <nav className=\"text-sm text-gray-500\">\n          <Link href=\"/\" className=\"hover:text-black\">Home</Link>\n          <span className=\"mx-2\">/</span>\n          <span className=\"text-black\">Returns</span>\n        </nav>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto px-4 py-12\">\n        <h1 className=\"text-4xl font-bold mb-8\">Returns & Exchanges</h1>\n\n        <div className=\"space-y-8\">\n          {/* Return Policy */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Return Policy</h2>\n            <div className=\"bg-gray-50 p-6 space-y-4\">\n              <p className=\"text-gray-600\">\n                We want you to be completely satisfied with your purchase. If you're not happy with your order, \n                you can return it within 28 days of delivery for a full refund.\n              </p>\n              <ul className=\"list-disc list-inside space-y-2 text-gray-600\">\n                <li>Items must be in original condition with all tags attached</li>\n                <li>Items must be unworn and in original packaging</li>\n                <li>Personalized or made-to-order items cannot be returned</li>\n                <li>Underwear, swimwear, and pierced jewelry cannot be returned for hygiene reasons</li>\n              </ul>\n            </div>\n          </section>\n\n          {/* How to Return */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">How to Return</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              <div>\n                <h3 className=\"font-medium mb-3\">UK Returns</h3>\n                <ol className=\"list-decimal list-inside space-y-2 text-sm text-gray-600\">\n                  <li>Log into your account and select the items to return</li>\n                  <li>Print your prepaid return label</li>\n                  <li>Package items securely in original packaging</li>\n                  <li>Attach the return label and drop off at any Royal Mail post office</li>\n                </ol>\n                <p className=\"text-sm font-medium mt-3\">FREE returns for UK customers</p>\n              </div>\n              <div>\n                <h3 className=\"font-medium mb-3\">International Returns</h3>\n                <ol className=\"list-decimal list-inside space-y-2 text-sm text-gray-600\">\n                  <li>Contact our customer service team</li>\n                  <li>We'll provide return instructions and label</li>\n                  <li>Package items securely</li>\n                  <li>Send via your local postal service</li>\n                </ol>\n                <p className=\"text-sm font-medium mt-3\">Return shipping costs apply</p>\n              </div>\n            </div>\n          </section>\n\n          {/* Exchanges */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Exchanges</h2>\n            <div className=\"bg-gray-50 p-6\">\n              <p className=\"text-gray-600 mb-4\">\n                We don't offer direct exchanges. To exchange an item, please return your original purchase \n                and place a new order for the item you want.\n              </p>\n              <p className=\"text-sm text-gray-600\">\n                This ensures you receive your new item as quickly as possible and aren't charged twice.\n              </p>\n            </div>\n          </section>\n\n          {/* Refunds */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Refunds</h2>\n            <div className=\"space-y-4\">\n              <p className=\"text-gray-600\">\n                Once we receive your return, we'll inspect the items and process your refund within 5-10 working days.\n              </p>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h3 className=\"font-medium mb-2\">Refund Methods</h3>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    <li>• Original payment method</li>\n                    <li>• Store credit (if preferred)</li>\n                    <li>• Gift card (for gift purchases)</li>\n                  </ul>\n                </div>\n                <div>\n                  <h3 className=\"font-medium mb-2\">Processing Times</h3>\n                  <ul className=\"text-sm text-gray-600 space-y-1\">\n                    <li>• Credit/Debit cards: 3-5 working days</li>\n                    <li>• PayPal: 1-2 working days</li>\n                    <li>• Bank transfer: 5-7 working days</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </section>\n\n          {/* Damaged Items */}\n          <section>\n            <h2 className=\"text-2xl font-semibold mb-4\">Damaged or Faulty Items</h2>\n            <div className=\"bg-red-50 border border-red-200 p-6\">\n              <p className=\"text-gray-700 mb-4\">\n                If you receive a damaged or faulty item, please contact us immediately. We'll arrange for a \n                replacement or full refund, including return shipping costs.\n              </p>\n              <p className=\"text-sm text-gray-600\">\n                Please take photos of any damage and include them when contacting our customer service team.\n              </p>\n            </div>\n          </section>\n\n          {/* Contact */}\n          <section className=\"bg-gray-50 p-6 text-center\">\n            <h3 className=\"font-semibold mb-2\">Need help with a return?</h3>\n            <p className=\"text-gray-600 mb-4\">Our customer service team is here to help</p>\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4\">\n              <Link href=\"/help\" className=\"luxury-button-outline\">\n                Contact Us\n              </Link>\n              <button className=\"luxury-button\">\n                Start Return\n              </button>\n            </div>\n          </section>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAmB;;;;;;sCAC5C,8OAAC;4BAAK,WAAU;sCAAO;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAa;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCAExC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAI7B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAMV,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;kEAEN,8OAAC;wDAAE,WAAU;kEAA2B;;;;;;;;;;;;0DAE1C,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;kEAEN,8OAAC;wDAAE,WAAU;kEAA2B;;;;;;;;;;;;;;;;;;;;;;;;0CAM9C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAIlC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAOzC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAG7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmB;;;;;;0EACjC,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;;;;;;;;;;;;;kEAGR,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmB;;;;;;0EACjC,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;kFACJ,8OAAC;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQd,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAIlC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAOzC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAwB;;;;;;0DAGrD,8OAAC;gDAAO,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/matches-headless-ui/matches-headless-ui/matches-fashion-clone/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,IAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}